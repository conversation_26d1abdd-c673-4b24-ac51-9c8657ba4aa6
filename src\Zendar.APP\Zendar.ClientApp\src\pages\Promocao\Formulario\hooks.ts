import { useCallback, useEffect, useRef, useState } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { setDateMaxHours, setDateMinHours } from 'helpers/data/setHoursDate';
import { validarListaServicos } from 'helpers/validation/validarListaServicos';

import api, { ResponseApi } from 'services/api';
import { buscarPromocaoAtiva } from 'services/tray';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';

import { ModalExplicacaoHorariosDiasTray } from './Components/ModalExplicacaoHorariosDiasTray';
import { telasExibicaoPromocao } from './constants';
import { FormData, ProdutoPromocaoProps } from './validationForms';

export const useFormularioPromocao = () => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const { control, handleSubmit, watch, getValues, setValue } =
    useFormContext<FormData>();

  const [bloquearAlteracaoTray, setBloquearAlteracaoTray] = useState(false);

  const { id: idRota } = useParams<{ id: string }>();

  const [
    watchLojas,
    watchTelasExibicao,
    watchDiasAtividade,
    watchDesconsiderarDias,
    watchDesconsiderarHorario,
  ] = watch([
    'lojas',
    'telasExibicao',
    'diasAtividade',
    'desconsiderarDias',
    'desconsiderarHorario',
  ]);

  const {
    fields: listaDiasAtividade,
    update: atualizarListaDiasAtividade,
    replace: setarListaDiasAtividade,
  } = useFieldArray({
    control,
    name: 'diasAtividade',
  });

  const {
    fields: listaTelasExibicao,
    update: atualizarListaTelasExibicao,
    replace: setarListaTelasExibicao,
  } = useFieldArray({
    control,
    name: 'telasExibicao',
  });

  const { fields: listaLojas, update: atualizarListaLojas } = useFieldArray({
    control,
    name: 'lojas',
  });

  const todosDiasDeAtividadeEstaoSelecionados = listaDiasAtividade.every(
    ({ selecionado }) => selecionado
  );

  const todasTelasExibicaoEstaoSelecionadas = listaTelasExibicao.every(
    ({ selecionado }) => selecionado
  );

  const possuiLojaSelecionada = watchLojas?.some(
    ({ selecionado }) => selecionado
  );

  const possuiDiaAtividadeSelecionado = watchDiasAtividade?.some(
    ({ selecionado }) => selecionado
  );

  const possuiTelaExibicaoSelecionada = watchTelasExibicao?.some(
    ({ selecionado }) => selecionado
  );

  const possuiServicoFrenteCaixa = validarListaServicos([
    enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
    enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
  ]);

  const possuiServicoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const alternarSelecaoTelaMesasOuComandaGarcom = (selecionado: boolean) => {
    setValue(
      'telasExibicao',
      listaTelasExibicao.map((tela) => {
        if (tela.value === telasExibicaoPromocao.COMANDA_GARCOM) {
          return {
            ...tela,
            selecionado,
          };
        }

        if (tela.value === telasExibicaoPromocao.MESAS) {
          return {
            ...tela,
            selecionado,
          };
        }

        return tela;
      })
    );
  };

  const alternarSelecaoTodosDiasDeAtividade = () => {
    const fields = listaDiasAtividade.map((dia) => ({
      ...dia,
      selecionado: !todosDiasDeAtividadeEstaoSelecionados,
    }));

    setarListaDiasAtividade(fields);
  };

  const alterarSelecaoDiaAtividade = (
    diaIndex: number,
    selecionado: boolean
  ) => {
    const field = listaDiasAtividade[diaIndex];

    atualizarListaDiasAtividade(diaIndex, {
      ...field,
      selecionado,
    });
  };

  const alternarSelecaoTodasTelasExibicao = () => {
    const fields = listaTelasExibicao.map((tela) => ({
      ...tela,
      selecionado:
        tela.value === telasExibicaoPromocao.TRAY && bloquearAlteracaoTray
          ? true
          : !todasTelasExibicaoEstaoSelecionadas,
    }));

    setarListaTelasExibicao(fields);
  };

  const abrirModalExplicacaoHorariosDiasTray = useCallback(() => {
    ModalExplicacaoHorariosDiasTray();
  }, []);

  const alterarSelecaoTelaExibicao = (
    telaIndex: number,
    selecionado: boolean
  ) => {
    const field = listaTelasExibicao[telaIndex];

    if (selecionado && field.value === telasExibicaoPromocao.TRAY) {
      abrirModalExplicacaoHorariosDiasTray();
    }
    if (
      field.value === telasExibicaoPromocao.COMANDA_GARCOM ||
      field.value === telasExibicaoPromocao.MESAS
    ) {
      // A tela de Mesas e Comanda Garçom são atreladas, quando uma é selecionada a outra também é
      alternarSelecaoTelaMesasOuComandaGarcom(selecionado);
      return;
    }

    atualizarListaTelasExibicao(telaIndex, {
      ...field,
      selecionado,
    });
  };

  const alterarSelecaoLoja = (lojaIndex: number, selecionado: boolean) => {
    const field = listaLojas[lojaIndex];

    atualizarListaLojas(lojaIndex, {
      ...field,
      selecionado,
    });
  };

  const alternarPreencherDias = () => {
    const fields = listaDiasAtividade.map((dia) => ({
      ...dia,
      selecionado: true,
    }));

    setarListaDiasAtividade(fields);
    setValue('desconsiderarDias', !watchDesconsiderarDias);
  };

  const alternarPreencherHorario = () => {
    setValue(
      'horarioVigenciaInicio',
      setDateMinHours(new Date()).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })
    );

    setValue(
      'horarioVigenciaFim',
      setDateMaxHours(new Date()).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })
    );

    setValue('desconsiderarHorario', !watchDesconsiderarHorario);
  };

  const formatHourUtcTime = (hour: string) => {
    const dataAtual = new Date();
    const [horas, minutos] = hour.split(':').map(Number);
    dataAtual.setHours(horas - 3, minutos, 0, 0);
    return dataAtual.toISOString();
  };

  const formatPeriodo = (date: string) => {
    const dataAtual = new Date(date);
    const dia = dataAtual.getDate();

    dataAtual.setDate(dia + 1);
    dataAtual.setHours(0, 0, 0, 0);
    dataAtual.setUTCHours(0, 0, 0, 0);

    return dataAtual.toISOString();
  };

  const validandoItensObrigatorios = useCallback(async () => {
    let hasErrors = false;

    const onSubmit = handleSubmit(
      () => {},
      () => {
        hasErrors = true;
      }
    );

    await onSubmit();

    return hasErrors;
  }, [handleSubmit]);

  const cadastrarProdutos = useCallback(
    async (id: string, data: ProdutoPromocaoProps[]) => {
      const response = await api.post<void, ResponseApi<string>>(
        ConstanteEnderecoWebservice.CADASTRAR_PRODUTO_PROMOCAO.replace(
          'id',
          id
        ),
        data
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
          return false;
        }

        if (response?.sucesso) {
          return true;
        }
      }

      return false;
    },
    []
  );

  const configuracaoPromocao = useCallback(async () => {
    const hasErrors = await validandoItensObrigatorios();

    if (hasErrors) {
      toast.warning(
        'Preencha os campos obrigatórios antes de informar os produtos'
      );
      return false;
    }

    if (!possuiLojaSelecionada) {
      toast.warning('Selecione ao menos uma loja para participar da promoção');
      return false;
    }

    if (!possuiTelaExibicaoSelecionada) {
      toast.warning('Selecione ao menos uma tela de exibição da campanha');
      return false;
    }

    if (!possuiDiaAtividadeSelecionado) {
      toast.warning('Selecione ao menos um dia de atividade');
      return false;
    }

    const data = getValues() as FormData;

    const valoresDiasAtividadeSelecionados = data.diasAtividade
      .filter(({ selecionado }) => selecionado)
      .map(({ value }) => value);

    const valoresTelasExibicaoSelecionadas = data.telasExibicao
      .filter(({ selecionado }) => selecionado)
      .map(({ value }) => value);

    const idLojasSelecionadas = data.lojas
      .filter(({ selecionado }) => selecionado)
      .map(({ lojaId }) => lojaId);

    const horarioVigenciaFim = data.horarioVigenciaFim
      ? String(data.horarioVigenciaFim)
      : String(
          new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })
        );

    const horarioVigenciaInicio = data.horarioVigenciaInicio
      ? String(data.horarioVigenciaInicio)
      : String(
          new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })
        );

    return {
      ativo: data.ativo,
      nome: data.nome,
      lojas: idLojasSelecionadas,
      diasDaSemana: valoresDiasAtividadeSelecionados,
      periodoVigenciaInicio: formatPeriodo(data.periodoVigenciaInicio),
      periodoVigenciaFim: formatPeriodo(data.periodoVigenciaFim),
      horarioVigenciaFim: formatHourUtcTime(horarioVigenciaFim),
      horarioVigenciaInicio: formatHourUtcTime(horarioVigenciaInicio),
      telasUsoPromocao: valoresTelasExibicaoSelecionadas,
      desconsiderarDias: data.desconsiderarDias,
      desconsiderarHorario: data.desconsiderarHorario,
    };
  }, [
    validandoItensObrigatorios,
    possuiLojaSelecionada,
    possuiTelaExibicaoSelecionada,
    getValues,
    possuiDiaAtividadeSelecionado,
  ]);

  const obterPromocaoAtivaNaTray = useCallback(async () => {
    const idPromocaoTray = await buscarPromocaoAtiva();
    if (idPromocaoTray === idRota) {
      setBloquearAlteracaoTray(true);
      return;
    }
    setBloquearAlteracaoTray(false);
  }, []);

  useEffect(() => {
    obterPromocaoAtivaNaTray();
  }, [obterPromocaoAtivaNaTray]);

  return {
    buttonRef,
    cadastrarProdutos,
    possuiServicoFrenteCaixa,
    possuiServicoTray,
    configuracaoPromocao,
    listaDiasAtividade,
    todosDiasDeAtividadeEstaoSelecionados,
    alternarSelecaoTodosDiasDeAtividade,
    alterarSelecaoDiaAtividade,
    listaTelasExibicao,
    todasTelasExibicaoEstaoSelecionadas,
    alternarSelecaoTodasTelasExibicao,
    alterarSelecaoTelaExibicao,
    listaLojas,
    alterarSelecaoLoja,
    watchDesconsiderarDias,
    watchDesconsiderarHorario,
    alternarPreencherDias,
    alternarPreencherHorario,
    bloquearAlteracaoTray,
  };
};
