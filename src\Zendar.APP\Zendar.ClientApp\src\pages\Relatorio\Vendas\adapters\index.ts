import { TipoFiscal } from 'constants/enum/tipoFiscal';

import { FormData } from '../validationForms';

export const adaptarGerarRelatorioVendas = (dados: FormData) => {
  const clienteFornecedorId = dados.clienteFornecedorId?.value || null;
  const entregadorId = dados.entregadorId?.value || null;
  const localEstoqueId = dados.localEstoqueId || undefined;
  debugger;
  const statusConsulta = dados.statusConsulta?.value || null;

  const tiposFiscaisSelecionaveis = [
    TipoFiscal.NFCE,
    TipoFiscal.NFE,
    TipoFiscal.SEM_FISCAL,
  ];

  const tiposFiscaisSelecionados =
    (dados.tipoFiscal?.length || 0) > 0 ? dados.tipoFiscal : [TipoFiscal.TODOS];

  const tipoFiscal = tiposFiscaisSelecionaveis.every((tipoFiscalEnum) =>
    tiposFiscaisSelecionados?.includes(tipoFiscalEnum)
  )
    ? [TipoFiscal.TODOS]
    : tiposFiscaisSelecionados;

  return {
    ...dados,
    clienteFornecedorId,
    entregadorId,
    tipoFiscal,
    localEstoqueId,
    statusConsulta,
  };
};
