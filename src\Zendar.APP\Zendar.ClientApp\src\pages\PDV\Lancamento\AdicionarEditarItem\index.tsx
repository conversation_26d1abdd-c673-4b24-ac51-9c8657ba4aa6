/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
import {
  Box,
  Grid,
  VStack,
  HStack,
  AspectRatio,
  Image,
  Flex,
  Button,
  Text,
  Icon,
  Fade,
  Input,
  useMediaQuery,
  useBreakpointValue,
  IconButton,
  useDisclosure,
  GridItem,
  Stack,
  FormLabel,
} from '@chakra-ui/react';
import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { isIOS as reactDeviceDetectIsIOS, isMobile } from 'react-device-detect';
import { useFormContext, FormProvider } from 'react-hook-form';
import { GlobalHotKeys } from 'react-hotkeys';
import { FiChevronDown, FiMinus, FiPlus } from 'react-icons/fi';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import getHeaderChaveTemporaria from 'helpers/api/getHeaderChaveTemporaria';
import cadastrarOperacao from 'helpers/api/Operacao/cadastrarOperacao';
import { OperacaoItemObter } from 'helpers/api/Operacao/obterOperacaoComItens';
import { usePromocaoPDV } from 'helpers/api/Operacao/promocao';
import vincularUsuarioDeLiberacao from 'helpers/api/Operacao/vincularUsuarioDeLiberacao';
import alterarOperacaoItem from 'helpers/api/OperacaoItem/alterarOperacaoItem';
import cadastrarOperacaoItem from 'helpers/api/OperacaoItem/cadastrarOperacaoItem';
import obterPrecoProduto from 'helpers/api/TabelaPreco/obterPrecoProduto';
import { moneyMask } from 'helpers/format/fieldsMasks';
import { blurIOS } from 'helpers/layout/blurIOS';
import { isIOS } from 'helpers/layout/isIOS';

import api, { ResponseApi } from 'services/api';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';
import { useInformacoesGeraisContext } from 'store/PDV/InformacoesGerais';
import { useLancamentoEditarItemContext } from 'store/PDV/LancamentoEditarItem';
import { useMenuContext } from 'store/PDV/Menu';
import { useOperacaoContext } from 'store/PDV/Operacao';
import SelectProdutoProvider, {
  GetPaginatedProductsOptionsParams,
  ProdutoOption,
  SelectProdutoContext,
} from 'store/PDV/SelectProduto';

import fontSizes from 'theme/foundations/fontSizes';

import { ButtonDefault } from 'components/Button/ButtonChakra';
import ManterFoco from 'components/Geral/ManterFoco';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ModalAtencao } from 'components/Modal/ModalAtencao';
import { ModalAutorizacaoDesconto } from 'components/Modal/ModalAutorizacaoDesconto';
import ButtonItemDesconto from 'components/PDV/ButtonItemDesconto';
import CampoContainer from 'components/PDV/Geral/CampoContainer';
import Overlay from 'components/PDV/Geral/Overlay';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import SelectProduto, {
  SelectProdutoRef,
} from 'components/PDV/Select/SelectProduto';
import { TextValor } from 'components/PDV/Text/TextValor';
import { InputNumber } from 'components/update/Input/InputNumber';
import { NumberInput } from 'components/update/Input/NumberInput';
import { ModalCodigoBarras } from 'components/update/Modal/ModalCodigoBarras';
import { PaginationData } from 'components/update/Pagination';
import { MobileSelectWrapperModal } from 'components/update/Select/MobileSelect/MobileSelectWrapperModal';

import OptionType from 'types/optionType';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import LogAuditoriaTelaEnum from 'constants/enum/logAuditoriaTela';
import OperacaoCamposAlterarEnum from 'constants/enum/operacaoCamposAlterar';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import TipoValorEnum, { TipoValor } from 'constants/enum/tipoValor';
import { SubstituirParametroRota } from 'constants/rotas';
import ConstanteRotasPDV from 'constants/rotasPDV';
import {
  TagPromocao,
  LeitorCodigoBarrasIcon,
  ProdutoSemImagemIcon,
  SalvarInserirNovoIcon,
  TagPromocaoMobile,
} from 'icons';

import { ModalInformacoesComplementares } from '../ModalInformacoesComplementares';

import { ModalAdicionarEditarItem } from './components/ModalAdicionarEditarItem';
import { ModalConsultaProdutos } from './components/ModalConsultaProdutos';
import { TamanhoOption, useProdutoLancamento } from './hooks';
import {
  AdicionarEditarItemProps,
  CalculoValoresItem,
  AlterarLocalEstoque,
  OperacaoItemObterInformacoesAlterar,
  AlterarItemOperacaoProps,
  LancarItemOperacaoProps,
} from './types';
import { useForm, yupResolver } from './validationForm';

const AdicionarEditarItem = ({
  chavePermissaoTemporaria,
}: AdicionarEditarItemProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const buscandoPrecoRef = useRef(false);
  const [valorDesconto, setValorDesconto] = useState(0);

  const asMobileView = useBreakpointValue({ base: true, md: false });

  const [isLargerThan360] = useMediaQuery('(min-width: 360px)');
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const [isLargerThan1800] = useMediaQuery('(min-width: 1800px)');

  const history = useHistory();
  const { setValue, getValues, watch } = useFormContext();
  const formMethods = useForm<any>({
    defaultValues: {
      quantidade: 1,
      tipoDescontoItem: TipoValorEnum.REAIS,
      descontoItem: 0,
      valorUnitario: 0,
      valorTotal: 0,
      valorPromocional: 0,
      valorUnitarioOriginal: 0,
      produto: undefined,
      produtoCorTamanhoId: '',
      observacao: '',
      identificacaoTipoOperacao: '',
    },
    resolver: yupResolver,
    shouldUnregister: false,
  });

  const {
    contaFinanceira,
    operacaoItemlength,
    localDeEstoqueSelecionado,
    ultimoEstoqueSelecionado,
    detalhesTroca,
    tabelaPrecoId,
    descontoMaximoUsuarioLogado,
    possivelAplicarDesconto,
    usuarioLiberacaoDesconto,
    setUsuarioLiberacaoDesconto,
    usuarioLogadoDiferenteDeUsuarioDaOperacao,
  } = useInformacoesGeraisContext();
  const { operacaoId, operacaoIsLoading } = useOperacaoContext();
  const { obterProdutosLancamento } = useProdutoLancamento();
  const { obterPrecoPromocoes, encontrarMenorPreco } = usePromocaoPDV();
  const { casasDecimais, obterCasasDecimais } = usePadronizacaoContext();
  const { operacaoItemEditando, setOperacaoItemEditando } =
    useLancamentoEditarItemContext();
  const { refButtonConsultaProdutos } = useMenuContext();

  const houveAlteracaoLocalEstoque =
    ultimoEstoqueSelecionado.current.id !== localDeEstoqueSelecionado.id &&
    !operacaoItemlength;

  const {
    isOpen: isModalSelectProdutoOpen,
    onOpen: onModalSelectProdutoOpen,
    onClose: onModalSelectProdutoClose,
  } = useDisclosure({ defaultIsOpen: !operacaoId });

  const {
    isOpen: isModalCodigoBarrasOpen,
    onOpen: onModalCodigoBarrasOpen,
    onClose: onModalCodigoBarrasClose,
  } = useDisclosure();

  const keyMap = {
    F8: ['f8'],
  };

  const handlersHotKeys = {
    F8: (event?: KeyboardEvent) => {
      if (event) event.preventDefault();
      if (refButtonConsultaProdutos.current) {
        refButtonConsultaProdutos.current?.click();
      }
    },
  };

  const selectProdutoRef = useRef<SelectProdutoRef>(null);

  const [
    quantidade,
    valorUnitario,
    descontoItem,
    tipoDescontoItem,
    valorUnitarioOriginal,
    produto,
    produtoCorTamanhoId,
  ] = formMethods.watch([
    'quantidade',
    'valorUnitario',
    'descontoItem',
    'tipoDescontoItem',
    'valorUnitarioOriginal',
    'produto',
    'produtoCorTamanhoId',
  ]);

  const tamanhosProduto: TamanhoOption[] = useMemo(
    () => produto?.tamanhos || [],
    [produto?.tamanhos]
  );
  const produtoConsignado = produto?.itemConsignado || false;
  const fotoProduto = produto?.foto || '';
  const nomeProduto = produto?.nome || '';
  const valorMaximoQuantidade = Math.ceil(999999999 / valorUnitario);
  const valorMaximoValorUnitario = Math.ceil(999999999 / quantidade);

  const viewIsLoading = isLoading || operacaoIsLoading;

  const tabelaPreco = watch('tabelaPreco');
  const vendedor = watch('vendedor');
  const possuiMovimentacaoFinanceira = watch('possuiMovimentacaoFinanceira');

  const naoPermitirAlterarValorNaVenda =
    !produto ||
    (produto && !produto.permiteAlterarValorNaVenda) ||
    produto?.tipoProduto === TipoProdutoEnum.PRODUTO_KIT;

  const possuiPromocao = produto?.precoPromocional;

  const produtoTemTamanhos = useMemo(() => {
    const tamanhosFiltrados = tamanhosProduto?.filter(
      (tamanho) => !tamanho.padraoSistema
    );
    return tamanhosFiltrados?.length > 0;
  }, [tamanhosProduto]);

  const latestProps = useRef({
    formMethods,
    produtoTemTamanhos,
    chavePermissaoTemporaria,
    operacaoItemEditando,
    setFocus: formMethods.setFocus,
  });

  function trocarFocoAoLancarProduto(
    produtoSendoAdicionadoPelaConsultaProdutos: boolean
  ) {
    if (produto) {
      setTimeout(() => {
        try {
          if (produtoSendoAdicionadoPelaConsultaProdutos) return;
          if (latestProps.current.produtoTemTamanhos) {
            latestProps.current.setFocus('produtoCorTamanhoId');
          } else if (!isMobile) {
            latestProps.current.setFocus('quantidade');
          }
        } catch {
          // continue regardless of error
        }
      }, 0);
    }
  }

  const limparProduto = useCallback(() => {
    latestProps.current.formMethods.setValue('produto', undefined);
    latestProps.current.formMethods.setValue('produtoCorTamanhoId', '');
  }, []);

  const limparValores = useCallback(() => {
    latestProps.current.formMethods.setValue('quantidade', 1);
    latestProps.current.formMethods.setValue(
      'tipoDescontoItem',
      TipoValorEnum.REAIS
    );
    latestProps.current.formMethods.setValue('descontoItem', 0);
    latestProps.current.formMethods.setValue('valorUnitario', 0);
    latestProps.current.formMethods.setValue('valorTotal', 0);
    latestProps.current.formMethods.setValue('valorPromocional', 0);
    latestProps.current.formMethods.setValue('valorUnitarioOriginal', 0);
  }, []);

  const calcularDescontoItem = (
    tipoDesconto: TipoValor,
    descItem: number,
    valorItem: number
  ): number => {
    if (tipoDesconto === TipoValorEnum.REAIS) {
      setValorDesconto(0);
      return descItem;
    }

    const valorDescontoItem =
      Math.round(((descItem / 100) * valorItem + Number.EPSILON) * 100) / 100;

    setValorDesconto(valorDescontoItem);
    return valorDescontoItem;
  };

  const calcularValorItemSemDesconto = (
    qtd: number,
    valorUnit: number
  ): number => {
    return Number((qtd * valorUnit).toFixed(2));
  };

  const calcularValorItemComDesconto = useCallback(
    (
      qtd: number,
      valorUnit: number,
      tipoDesconto: TipoValor,
      descItem: number
    ): number => {
      let valorItem = calcularValorItemSemDesconto(qtd, valorUnit);
      valorItem -= calcularDescontoItem(tipoDesconto, descItem, valorItem);
      return Number(valorItem.toFixed(2));
    },
    []
  );

  const calcularValoresItem = useCallback(
    (operacaoItem: OperacaoItemObter): CalculoValoresItem => {
      const valorItemSemDesconto = calcularValorItemSemDesconto(
        operacaoItem.quantidade,
        operacaoItem.valorUnitario
      );

      const valorDescontoItem = calcularDescontoItem(
        operacaoItem.tipoDescontoItem,
        operacaoItem.descontoItem,
        valorItemSemDesconto
      );

      const valorItemComDesconto = calcularValorItemComDesconto(
        operacaoItem.quantidade,
        operacaoItem.valorUnitario,
        operacaoItem.tipoDescontoItem,
        operacaoItem.descontoItem
      );

      return { valorItemSemDesconto, valorDescontoItem, valorItemComDesconto };
    },
    [calcularValorItemComDesconto]
  );

  const construirDadosItem = useCallback(
    async (
      operacaoProps: OperacaoItemObter,
      valoresCalculados: CalculoValoresItem
    ) => {
      let informacoesComplementares = '';

      if (operacaoProps.solicitarInformacaoComplementar) {
        informacoesComplementares = await ModalInformacoesComplementares();
      }
      const dados = {
        operacaoId,
        tela: LogAuditoriaTelaEnum.PDV,
        quantidade: operacaoProps.quantidade,
        produtoCorTamanhoId: operacaoProps.produtoCorTamanhoId,
        campanhaPromocionalId: operacaoProps.campanhaPromocionalId,
        precoPromocional: operacaoProps.precoPromocional,
        valorDescontoItem: Number(
          valoresCalculados.valorDescontoItem.toFixed(2)
        ),
        valorUnitario: operacaoProps.valorUnitario,
        valorItemComDesconto: valoresCalculados.valorItemComDesconto,
        valorItemSemDesconto: valoresCalculados.valorItemSemDesconto,
        valorVendaOriginal: operacaoProps.valorVendaOriginal,
        tipoDescontoItem: operacaoProps.tipoDescontoItem,
        descontoItem: operacaoProps.descontoItem,
        informacoesComplementares,
      };
      return dados;
    },
    [operacaoId]
  );

  const lancarProdutoNaOperacao = useCallback(
    async (operacaoItem: OperacaoItemObter) => {
      if (possuiMovimentacaoFinanceira) {
        toast.warning(
          'Para adicionar um novo item é necessário remover o pagamento adicionado.'
        );
        return;
      }
      setIsLoading(true);

      const { userId: usuarioId } = auth.getDadosToken();

      const descontosItem = calcularValoresItem(operacaoItem);
      const item = await construirDadosItem(operacaoItem, descontosItem);
      if (!operacaoId) {
        const { id: vendedorId } = getValues('vendedor') || {};
        const { id: clienteFornecedorId } = getValues('cliente') || {};
        const { id: lojaId } = auth.getLoja();
        const observacao = getValues('observacao');
        const identificacaoTipoOperacao = getValues(
          'identificacaoTipoOperacao'
        );

        if (
          vendedorId &&
          clienteFornecedorId &&
          lojaId &&
          usuarioId &&
          contaFinanceira
        ) {
          const operacaoCriada = await cadastrarOperacao({
            vendedorId,
            clienteFornecedorId,
            lojaId,
            usuarioId,
            tela: LogAuditoriaTelaEnum.PDV,
            identificacaoTipoOperacao,
            observacao,
            caixaMovimentacaoId: contaFinanceira?.caixaMovimentacao?.id,
            tabelaPrecoId: tabelaPrecoId || tabelaPreco?.value,
            operacaoItem: item,
            devolucaoId: detalhesTroca.idOperacao || null,
            localEstoqueId: localDeEstoqueSelecionado.id,
          });

          if (operacaoCriada) {
            history.replace(
              SubstituirParametroRota(
                ConstanteRotasPDV.PDV_LANCAMENTO_ID,
                'id?',
                operacaoCriada.id
              )
            );

            return;
          }
        }
      }
      if (item.operacaoId) {
        const tamanhosDoProduto = formMethods.watch('produto.tamanhos') || [];
        const idItemAdicionado = await cadastrarOperacaoItem(item);
        if (idItemAdicionado) {
          const itensJaIncluidosNaOperacao = getValues('operacaoItens');
          const novoItemParaIncluir = {
            id: idItemAdicionado,
            ...operacaoItem,
            tamanho:
              tamanhosDoProduto?.length > 0 &&
              tamanhosDoProduto?.every(
                (tamanho: {
                  padraoSistema: boolean;
                  id: string;
                  nome: string;
                }) => tamanho.padraoSistema === true
              )
                ? ''
                : operacaoItem.tamanho || '',
            produto: operacaoItem.produto,
            valorItemComDesconto: item.valorItemComDesconto,
            valorItemSemDesconto: item.valorItemSemDesconto,
            valorDescontoItem: item.valorDescontoItem,
            valorUnitario: item.valorUnitario,
            valorVendaOriginal: item.valorVendaOriginal,
            informacoesComplementares: item.informacoesComplementares,
            campanhaPromocionalId: item.campanhaPromocionalId,
            precoPromocional: item.precoPromocional,
          } as OperacaoItemObter;

          setValue('operacaoItens', [
            ...(itensJaIncluidosNaOperacao || []),
            novoItemParaIncluir,
          ]);
          limparProduto();
        }
      }

      if (selectProdutoRef?.current) {
        selectProdutoRef.current.blur();
        selectProdutoRef.current.focus();
      }

      setIsLoading(false);
    },
    [
      possuiMovimentacaoFinanceira,
      calcularValoresItem,
      construirDadosItem,
      operacaoId,
      getValues,
      contaFinanceira,
      tabelaPrecoId,
      tabelaPreco?.value,
      detalhesTroca.idOperacao,
      localDeEstoqueSelecionado.id,
      history,
      formMethods,
      setValue,
      limparProduto,
    ]
  );

  const verificarAlteracaoNaPromocao = useCallback(
    (valorUnitarioProduto: number, valorPromocionalOriginal: number) => {
      const produtoAtual = formMethods.watch('produto');
      const usuarioAlterouPromocao = !produtoAtual.precoPromocional
        ? false
        : valorUnitarioProduto !== valorPromocionalOriginal;
      if (usuarioAlterouPromocao) {
        formMethods.setValue('produto', {
          ...produtoAtual,
          precoPromocional: false,
          campanhaPromocionalId: null,
        });
      }

      return usuarioAlterouPromocao;
    },
    [formMethods]
  );

  const alterarItemOperacao = async (
    operacaoItem: AlterarItemOperacaoProps
  ) => {
    setIsLoading(true);
    if (operacaoId && operacaoItemEditando) {
      const valorItemSemDesconto = calcularValorItemSemDesconto(
        operacaoItem.quantidade,
        operacaoItem.valorUnitario
      );
      const valorTotalDescontoItem = calcularDescontoItem(
        operacaoItem.tipoDescontoItem,
        operacaoItem.descontoItem,
        valorItemSemDesconto
      );

      const valorDescontoItem =
        Math.round((valorTotalDescontoItem + Number.EPSILON) * 100) / 100;

      const valorItemComDesconto = calcularValorItemComDesconto(
        operacaoItem.quantidade,
        operacaoItem.valorUnitario,
        operacaoItem.tipoDescontoItem,
        operacaoItem.descontoItem
      );

      const { produto: produtoOperacao } = operacaoItem;

      const { nome: tamanhoNome = '' } =
        produtoOperacao?.tamanhos && produtoOperacao?.tamanhos?.length > 0
          ? produtoOperacao?.tamanhos?.find(
              (tamanho: { id: string }) =>
                tamanho.id === operacaoItem?.produtoCorTamanhoId
            ) || {}
          : {};
      const houveAlteracaoValorProdutoPromocional =
        verificarAlteracaoNaPromocao(
          operacaoItem.valorUnitario,
          Number(operacaoItem.valorPromocional)
        );

      const possuiPrecoPromocional = possuiPromocao
        ? !houveAlteracaoValorProdutoPromocional
        : false;

      const promocaoId =
        operacaoItem?.produto?.campanhaPromocionalId ||
        operacaoItemEditando?.campanhaPromocional?.id;

      delete operacaoItemEditando.campanhaPromocional;
      const itemAlteradoRequest = {
        ...operacaoItemEditando,
        produtoCorTamanhoId: operacaoItem.produtoCorTamanhoId,
        quantidade: operacaoItem.quantidade,
        valorUnitario: operacaoItem.valorUnitario,
        valorItemComDesconto,
        valorItemSemDesconto,
        foto: produtoOperacao.foto,
        tamanho: tamanhoNome,
        tipoDescontoItem: operacaoItem.tipoDescontoItem,
        descontoItem: operacaoItem.descontoItem,
        precoPromocional: possuiPrecoPromocional,
        campanhaPromocionalId: possuiPrecoPromocional ? promocaoId : null,
        valorDescontoItem,
        tela: LogAuditoriaTelaEnum.PDV,
        operacaoId,
      };

      const success = await alterarOperacaoItem(
        {
          ...itemAlteradoRequest,
        },
        operacaoItemEditando.chavePermissaoTemporariaAlterar
      );

      if (success) {
        const operacaoItens = getValues('operacaoItens') || [];
        const currentOperacaoItem = (
          operacaoItens as Array<{
            id: string;
          }>
        ).find((op: { id: string }) => op.id === operacaoItemEditando.id);
        const currentOperacaoItemIndex =
          operacaoItens.indexOf(currentOperacaoItem);

        operacaoItens.splice(currentOperacaoItemIndex, 1);
        operacaoItens.splice(currentOperacaoItemIndex, 0, {
          ...itemAlteradoRequest,
        });

        setValue('operacaoItens', [...operacaoItens]);

        setOperacaoItemEditando(undefined);
        limparProduto();
      }
    }

    setIsLoading(false);
  };

  async function alterarLocalEstoqueDaOperacao({
    campo,
    id,
    conteudoCampo,
    tela,
  }: AlterarLocalEstoque) {
    const data = {
      campo,
      id,
      conteudoCampo,
      tela,
    } as AlterarLocalEstoque;
    const response = await api.put<void, ResponseApi<string>>(
      ConstanteEnderecoWebservice.PEDIDOORCAMENTOVENDA_ALTERAR,
      {
        ...data,
      }
    );

    if (response.avisos) {
      response.avisos.forEach((avisos) => toast.warn(avisos));
    }

    if (response.sucesso) {
      ultimoEstoqueSelecionado.current = localDeEstoqueSelecionado;
    }
  }

  const casasDecimaisDoCampoQuantidade = useMemo(() => {
    if (operacaoItemEditando && operacaoItemEditando.volumeUnitario) {
      return 0;
    }

    if (operacaoItemEditando && !operacaoItemEditando.volumeUnitario) {
      return casasDecimais.casasDecimaisQuantidade;
    }

    if (produto && produto?.volumeUnitario) {
      return 0;
    }

    return casasDecimais.casasDecimaisQuantidade;
  }, [operacaoItemEditando, produto, casasDecimais]);

  const defaultProdutoCorTamanhoId = useCallback(
    (tamanhos: TamanhoOption[]) => {
      if (operacaoItemEditando?.tamanho) {
        return operacaoItemEditando.produtoCorTamanhoId;
      }
      if (tamanhos?.length > 0 && !tamanhos[0]?.padraoSistema) {
        return tamanhos[0]?.id;
      }
      return '';
    },
    [operacaoItemEditando]
  );

  const preencherCampoTamanho = useCallback(
    (tamanhos: TamanhoOption[], codigoInterno?: string) => {
      if (codigoInterno) {
        const productToAddAutomatically = tamanhos?.find(
          (tamanho) => tamanho.codigoBarrasInterno === codigoInterno
        );

        if (productToAddAutomatically) {
          latestProps.current.formMethods.setValue(
            'produtoCorTamanhoId',
            productToAddAutomatically.id
          );
          return;
        }
      }
      const somenteUmTamanho = tamanhos?.length === 1;
      const tamanhoId = somenteUmTamanho
        ? tamanhos[0]?.id
        : defaultProdutoCorTamanhoId(tamanhos);

      latestProps.current.formMethods.setValue(
        'produtoCorTamanhoId',
        tamanhoId
      );
    },
    [defaultProdutoCorTamanhoId]
  );

  const preencherCamposProduto = useCallback(
    (produtoCompleto: ProdutoOption) => {
      latestProps.current.formMethods.setValue('produto', produtoCompleto);
    },
    []
  );

  const validateCasasDecimais = useCallback(async () => {
    let casasDecimaisDefault = casasDecimais;
    if (!casasDecimais?.casasDecimaisValor) {
      const newCasasDecimais = await obterCasasDecimais();

      if (!newCasasDecimais?.casasDecimaisValor) {
        toast.error('Erro ao obter casas decimais');
        return 2;
      }

      casasDecimaisDefault = newCasasDecimais;
    }

    return casasDecimaisDefault.casasDecimaisValor;
  }, [casasDecimais, obterCasasDecimais]);

  const preencherValores = useCallback(
    ({
      precoProdutoOriginal,
      precoProdutoNaPromocao,
      casasDecimaisValor,
    }: {
      precoProdutoOriginal: number;
      precoProdutoNaPromocao: {
        precoPromocional: number;
        promocaoId: string;
      } | null;
      casasDecimaisValor: number;
    }) => {
      const { setValue: setValueFormMethods, watch: watchFormMethods } =
        latestProps.current.formMethods;
      const produtoWatch = watchFormMethods('produto');
      const precoValorUnitarioFormatado = precoProdutoNaPromocao
        ? Number(
            precoProdutoNaPromocao.precoPromocional?.toFixed(casasDecimaisValor)
          )
        : Number(precoProdutoOriginal?.toFixed(casasDecimaisValor)) || 0;

      setValueFormMethods(
        'valorUnitarioOriginal',
        precoProdutoOriginal?.toFixed(casasDecimaisValor)
      );
      setValueFormMethods('valorUnitario', precoValorUnitarioFormatado);
      setValueFormMethods('valorTotal', precoValorUnitarioFormatado);
      setValueFormMethods(
        'valorPromocional',
        Number(
          precoProdutoNaPromocao?.precoPromocional?.toFixed(
            casasDecimaisValor
          ) ?? 0
        )
      );
      setValueFormMethods('produto', {
        ...produtoWatch,
        valorPromocional: precoProdutoNaPromocao?.precoPromocional || 0,
        campanhaPromocionalId: precoProdutoNaPromocao?.promocaoId || null,
        precoPromocional: precoProdutoNaPromocao?.promocaoId ? true : false,
      });
    },
    []
  );

  const obterPrecoProdutoSelecionado = useCallback(async () => {
    if (buscandoPrecoRef.current) {
      return;
    }

    buscandoPrecoRef.current = true;
    setIsLoading(true);

    try {
      if (!tabelaPrecoId) {
        return;
      }
      limparValores();
      const currentProdutoCorTamanhoId = latestProps.current.formMethods.watch(
        'produtoCorTamanhoId'
      );

      if (!currentProdutoCorTamanhoId) {
        return;
      }

      const casasDecimaisValor = await validateCasasDecimais();
      const precoProdutoOriginal = await obterPrecoProduto(
        currentProdutoCorTamanhoId,
        tabelaPrecoId
      );
      const promocoesProduto = await obterPrecoPromocoes(
        currentProdutoCorTamanhoId
      );
      const precoProdutoNaPromocao = encontrarMenorPreco(promocoesProduto);

      preencherValores({
        precoProdutoOriginal: precoProdutoOriginal || 0,
        precoProdutoNaPromocao,
        casasDecimaisValor,
      });
    } finally {
      setIsLoading(false);
      buscandoPrecoRef.current = false;
    }
  }, [
    tabelaPrecoId,
    limparValores,
    validateCasasDecimais,
    obterPrecoPromocoes,
    encontrarMenorPreco,
    preencherValores,
  ]);

  const buscarInformacoesProduto = useCallback(
    async (produtoInformacao: {
      label: string;
      value?: string;
      codigoBarrasInterno?: string;
    }) => {
      latestProps.current.formMethods.setValue('produto', null);
      const informacoesProduto = await obterProdutosLancamento(
        produtoInformacao as OptionType
      );

      if (informacoesProduto) {
        const tamanhosFiltered = informacoesProduto.tamanhos.filter(
          (itemTamanho) => !itemTamanho.padraoSistema
        );
        const novoProduto = {
          ...informacoesProduto,
          nome: produtoInformacao.label.split('|')[0],
          tamanhos:
            tamanhosFiltered.length > 0
              ? tamanhosFiltered
              : informacoesProduto.tamanhos,
        };

        preencherCamposProduto(novoProduto);
        preencherCampoTamanho(
          novoProduto?.tamanhos || [],
          produtoInformacao?.codigoBarrasInterno
        );
      }
    },
    [obterProdutosLancamento, preencherCampoTamanho, preencherCamposProduto]
  );

  const lancarItemOperacao = useCallback(
    async (data: LancarItemOperacaoProps) => {
      const { produto: produtoOperacao } = data;

      const { nome: tamanhoNome = '' } =
        produtoOperacao?.tamanhos && produtoOperacao?.tamanhos?.length > 0
          ? produtoOperacao?.tamanhos?.find(
              (tamanho: { id: string }) =>
                tamanho.id === data?.produtoCorTamanhoId
            ) || {}
          : {};
      const usuarioAlterouValorUnitario = verificarAlteracaoNaPromocao(
        Number(data?.valorUnitario),
        Number(data?.valorPromocional)
      );

      await lancarProdutoNaOperacao({
        descontoItem: data.descontoItem,
        produto: data?.produto?.nome,
        cor: data?.produto?.cor || '',
        tamanho: tamanhoNome,
        foto: data?.produto?.foto,
        tipoDescontoItem: data.tipoDescontoItem,
        permiteAlterarValorNaVenda: data?.produto?.permiteAlterarValorNaVenda,
        volumeUnitario: data?.produto?.volumeUnitario,
        solicitarInformacaoComplementar:
          data?.produto?.solicitarInformacaoComplementar,
        quantidade: data.quantidade,
        produtoCorTamanhoId: data.produtoCorTamanhoId,
        valorItemComDesconto: data.valorItemComDesconto,
        valorItemSemDesconto: data.valorItemSemDesconto,
        valorUnitario: Number(data.valorUnitario),
        valorVendaOriginal: Number(data?.valorUnitarioOriginal),
        valorPromocional: Number(data?.valorPromocional),
        campanhaPromocionalId: usuarioAlterouValorUnitario
          ? null
          : data?.produto?.campanhaPromocionalId,
        precoPromocional: usuarioAlterouValorUnitario
          ? false
          : data?.produto?.precoPromocional,
      } as OperacaoItemObter);
    },
    [lancarProdutoNaOperacao, verificarAlteracaoNaPromocao]
  );

  const validarLancamento = () => {
    const { valorTotal } = formMethods.getValues();
    if (possuiMovimentacaoFinanceira) {
      toast.warning(
        'Para adicionar um novo item é necessário remover o pagamento adicionado.'
      );
      return false;
    }

    if (valorTotal <= 0) {
      toast.warning('Não é possível adicionar um item sem valor.');
      return false;
    }

    return true;
  };

  const handleSubmit = formMethods.handleSubmit(
    async ({ valorTotal, ...data }) => {
      const operacaoItens =
        (watch('operacaoItens') as OperacaoItemObter[]) ??
        ([] as OperacaoItemObter[]);

      const valorProdutoAtualSemDesconto = calcularValorItemSemDesconto(
        data.quantidade,
        data.valorUnitario
      );

      let valorTotalItensOperacao =
        operacaoItens.reduce(
          (acc, curr) => (acc += curr.valorUnitario * curr.quantidade),
          0
        ) + valorProdutoAtualSemDesconto;

      let valorTotalDescontosOperacao =
        operacaoItens.reduce((acc, item) => {
          return (acc +=
            item.tipoDescontoItem === TipoValorEnum.REAIS
              ? item.valorDescontoItem
              : (item.descontoItem / 100) *
                (item.quantidade * item.valorUnitario));
        }, 0) +
        (tipoDescontoItem === TipoValorEnum.REAIS
          ? data.descontoItem
          : (data.descontoItem / 100) * (data.quantidade * data.valorUnitario));

      if (operacaoItemEditando) {
        valorTotalItensOperacao -= calcularValorItemSemDesconto(
          operacaoItemEditando.quantidade,
          operacaoItemEditando.valorUnitario
        );

        valorTotalDescontosOperacao -= operacaoItemEditando.valorDescontoItem;
      }

      const descontoMaximoUtilizado = usuarioLiberacaoDesconto
        ? usuarioLiberacaoDesconto.descontoMaximoPermitido
        : descontoMaximoUsuarioLogado;

      const descontoLiberado = possivelAplicarDesconto(
        valorTotalItensOperacao,
        valorTotalDescontosOperacao,
        descontoMaximoUtilizado
      );

      onModalSelectProdutoClose();
      if (!validarLancamento) return;

      if (
        (!operacaoItemEditando && data.descontoItem !== 0) ||
        (operacaoItemEditando &&
          (data.descontoItem !== operacaoItemEditando?.descontoItem ||
            data.tipoDescontoItem !== operacaoItemEditando?.tipoDescontoItem))
      ) {
        if (descontoLiberado && usuarioLogadoDiferenteDeUsuarioDaOperacao) {
          await vincularUsuarioDeLiberacao({
            operacaoId: operacaoId ?? '',
            usuarioLiberacaoId: auth.getUsuario().id,
          });
        }

        if (!descontoLiberado) {
          await ModalAtencao({
            title: 'Desconto acima do limite permitido',
            text: `O limite de desconto permitido para este usuário é de ${descontoMaximoUtilizado}%. Para liberar o valor exclusivamente para esta venda, insira os dados do administrador ou de outro usuário com permissão para aplicar um valor maior.`,
            showCancelButton: true,
            confirmButtonText: 'Autorizar desconto',
            cancelButtonText: 'Voltar',
            focusCancel: true,
            callback: async () => {
              const responseAutorizacao = await ModalAutorizacaoDesconto();

              if (responseAutorizacao === null) {
                return toast.error(
                  'O usuário informado não possui a permissão para aplicar descontos ou o desconto máximo é 0 (zero)'
                );
              }

              const possivelAplicarDescontoComUsuarioDeLiberacao =
                possivelAplicarDesconto(
                  valorTotalItensOperacao,
                  valorTotalDescontosOperacao,
                  responseAutorizacao.descontoMaximo
                );

              if (!possivelAplicarDescontoComUsuarioDeLiberacao) {
                return toast.error(
                  'O usuário informado não possui o desconto máximo necessário para aplicar o desconto'
                );
              }

              localStorage.setItem(
                'pdv-operacao-inicial-usuarioLiberacaoId',
                responseAutorizacao.id
              );

              setUsuarioLiberacaoDesconto({
                id: responseAutorizacao.id,
                nome: responseAutorizacao.nome,
                descontoMaximoPermitido: responseAutorizacao.descontoMaximo,
              });

              if (operacaoItemEditando) {
                alterarItemOperacao({
                  ...data,
                  volumeUnitario: operacaoItemEditando.volumeUnitario,
                });

                await vincularUsuarioDeLiberacao({
                  operacaoId: operacaoId ?? '',
                  usuarioLiberacaoId: responseAutorizacao.id,
                });
              } else if (produto) {
                const dataToBeSendForApiChangeLocalStock = {
                  conteudoCampo: localDeEstoqueSelecionado.id,
                  campo: OperacaoCamposAlterarEnum.LOCAL_ESTOQUE,
                  tela: LogAuditoriaTelaEnum.PDV,
                  id: operacaoId,
                } as AlterarLocalEstoque;

                if (houveAlteracaoLocalEstoque && operacaoId)
                  await alterarLocalEstoqueDaOperacao(
                    dataToBeSendForApiChangeLocalStock
                  );

                await lancarItemOperacao(data);
              }
            },
          });
        }
      }

      if (operacaoItemEditando) {
        alterarItemOperacao({
          ...data,
          volumeUnitario: operacaoItemEditando.volumeUnitario,
        });
      } else if (produto) {
        const dataToBeSendForApiChangeLocalStock = {
          conteudoCampo: localDeEstoqueSelecionado.id,
          campo: OperacaoCamposAlterarEnum.LOCAL_ESTOQUE,
          tela: LogAuditoriaTelaEnum.PDV,
          id: operacaoId,
        } as AlterarLocalEstoque;

        if (houveAlteracaoLocalEstoque && operacaoId)
          await alterarLocalEstoqueDaOperacao(
            dataToBeSendForApiChangeLocalStock
          );
        await lancarItemOperacao(data);
      }
    }
  );

  function handleDecrementarQuantidade() {
    const novaQuantidade = quantidade - 1;
    formMethods.setValue(
      'quantidade',
      novaQuantidade >= 1 ? novaQuantidade : 1
    );
  }

  function handleIncrementarQuantidade() {
    formMethods.setValue('quantidade', quantidade + 1);
  }

  const buscarProdutoCor = async (produtoCor: {
    value: string;
    label: string;
  }) => {
    const response = await api.get<
      void,
      ResponseApi<
        {
          id: string;
        }[]
      >
    >(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoCor.value}/cores`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        return response.dados[0]?.id;
      }
    }
  };

  const handleAbrirConsultaProdutos = async ({
    getPaginatedProductsOptions,
    handleSelectProdutoChange,
    setInputValue,
  }: {
    getPaginatedProductsOptions: (
      params: GetPaginatedProductsOptionsParams
    ) => Promise<{
      options: OptionType[];
    }>;
    handleSelectProdutoChange: (value: {
      label: string;
      value: string;
      codigoBarrasInterno?: string;
    }) => void;
    setInputValue: (value: string) => void;
  }) => {
    try {
      const produtoEscolhidoConsulta = await ModalConsultaProdutos({
        casasDecimais,
      });

      if (
        produtoEscolhidoConsulta.tipoProduto === TipoProdutoEnum.PRODUTO_SIMPLES
      ) {
        const produtoCorId = await buscarProdutoCor(produtoEscolhidoConsulta);
        selectProdutoRef.current?.atualizarValor({
          value: produtoEscolhidoConsulta.value,
          label: produtoEscolhidoConsulta.label,
        });
        await buscarInformacoesProduto({
          value: produtoCorId,
          label: produtoEscolhidoConsulta.label,
        });
        latestProps.current.setFocus('quantidade');

        return;
      }
      if (produtoEscolhidoConsulta.codigoBarrasInterno) {
        const dataPagination = {
          currentPage: 1,
          orderColumn: '',
          orderDirection: 'asc',
          pageSize: 20,
        } as PaginationData;

        const produtoBuscado = await getPaginatedProductsOptions({
          search: produtoEscolhidoConsulta.codigoBarrasInterno,
          dataPagination,
          allowSearchOnlyWithNumbers: true,
        });

        if (produtoBuscado.options?.length === 1) {
          setInputValue(produtoBuscado.options[0].label);
          selectProdutoRef.current?.atualizarValor({
            value: produtoBuscado.options[0].value,
            label: produtoBuscado.options[0].label,
          });

          handleSelectProdutoChange({
            label: produtoBuscado.options[0].label,
            value: produtoBuscado.options[0].value,
            codigoBarrasInterno: produtoEscolhidoConsulta.codigoBarrasInterno,
          });
          setTimeout(() => {
            latestProps.current.setFocus('quantidade');
          }, 500);
        }
      }
    } catch (error) {
      if (selectProdutoRef?.current) {
        selectProdutoRef.current.blur();

        if (selectProdutoRef.current) {
          setTimeout(() => {
            selectProdutoRef?.current?.focus();
          }, 200);
        }
      }
    }
  };

  useEffect(() => {
    latestProps.current = {
      formMethods,
      produtoTemTamanhos,
      chavePermissaoTemporaria,
      operacaoItemEditando,
      setFocus: formMethods.setFocus,
    };
  });

  useEffect(() => {
    const valorItemSemDesconto = calcularValorItemSemDesconto(
      quantidade,
      valorUnitario as number
    );

    const valorDescontoItem = calcularDescontoItem(
      tipoDescontoItem,
      descontoItem as number,
      valorItemSemDesconto
    );

    if (valorItemSemDesconto < valorDescontoItem) {
      if (tipoDescontoItem === TipoValorEnum.PORCENTAGEM) {
        latestProps.current.formMethods.setValue('descontoItem', 100);
      } else {
        latestProps.current.formMethods.setValue(
          'descontoItem',
          valorItemSemDesconto.toFixed(2)
        );
      }

      return;
    }

    const valorItemComDesconto = calcularValorItemComDesconto(
      quantidade,
      valorUnitario as number,
      tipoDescontoItem,
      descontoItem as number
    );

    latestProps.current.formMethods.setValue(
      'valorTotal',
      valorItemComDesconto.toFixed(2)
    );
  }, [
    quantidade,
    valorUnitario,
    descontoItem,
    tipoDescontoItem,
    calcularValorItemComDesconto,
  ]);

  useEffect(() => {
    const valorItemComDesconto = calcularValorItemComDesconto(
      quantidade,
      valorUnitario as number,
      tipoDescontoItem,
      descontoItem as number
    );

    formMethods.setValue('valorTotal', valorItemComDesconto.toFixed(2));
  }, [
    quantidade,
    formMethods,
    valorUnitario,
    descontoItem,
    tipoDescontoItem,
    calcularValorItemComDesconto,
  ]);

  useEffect(() => {
    const handleObterInformacoesOperacaoItemEditando = async () => {
      if (operacaoItemEditando) {
        setIsLoading(true);
        limparValores();
        const response = await api.get<
          void,
          ResponseApi<OperacaoItemObterInformacoesAlterar>
        >(
          ConstanteEnderecoWebservice.PEDIDOORCAMENTOVENDA_ITEM_OBTER_INFORMACOES_ALTERAR,
          {
            params: { id: operacaoItemEditando.id },
            ...getHeaderChaveTemporaria(
              latestProps.current.chavePermissaoTemporaria
            ),
          }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.map((item: string) => toast.warning(item));
          }
          if (response.sucesso && response.dados) {
            latestProps.current.formMethods.reset({
              produtoCorTamanhoId: operacaoItemEditando.produtoCorTamanhoId,
              quantidade: operacaoItemEditando.quantidade,
              tipoDescontoItem: operacaoItemEditando.tipoDescontoItem,
              valorUnitario: operacaoItemEditando.valorUnitario?.toFixed(
                casasDecimais.casasDecimaisValor
              ),
              descontoItem: operacaoItemEditando.descontoItem,
              valorTotal: operacaoItemEditando.valorItemComDesconto,
              valorUnitarioOriginal:
                operacaoItemEditando.valorVendaOriginal?.toFixed(
                  casasDecimais.casasDecimaisValor
                ),
              valorPromocional: Number(
                operacaoItemEditando.valorPromocional
              )?.toFixed(casasDecimais.casasDecimaisValor),
            });

            const produtoEditando = {
              nome: operacaoItemEditando.produto,
              adicionarItemAutomaticamente: false,
              volumeUnitario: !!operacaoItemEditando.valorUnitario,
              cor: operacaoItemEditando.cor || '',
              foto: response.dados.foto,
              permiteAlterarValorNaVenda:
                response.dados.permiteAlterarValorNaVenda,
              tamanhos: response.dados.tamanhos,
              solicitarInformacaoComplementar: false,
              itemConsignado: response.dados.itemConsignado,
              tipoProduto: response.dados.tipoProduto,
              precoPromocional: operacaoItemEditando.precoPromocional,
              campanhaPromocionalId: operacaoItemEditando.campanhaPromocionalId,
            };

            formMethods.setValue('produto', produtoEditando);
          }
        }
        setIsLoading(false);
      }
    };

    if (operacaoItemEditando) {
      handleObterInformacoesOperacaoItemEditando();
    }
  }, [
    casasDecimais.casasDecimaisValor,
    formMethods,
    limparValores,
    operacaoItemEditando,
  ]);

  useEffect(() => {
    (async () => {
      if (!operacaoIsLoading) {
        blurIOS(() => {
          if (selectProdutoRef.current) {
            selectProdutoRef.current.focus();
          }
        });
      }
    })();
  }, [operacaoIsLoading]);

  useEffect(() => {
    if (produto) return;
    if (isIOS() || reactDeviceDetectIsIOS) {
      if (document.activeElement) {
        (document.activeElement as HTMLInputElement).blur();
      }
    } else if (selectProdutoRef.current && !reactDeviceDetectIsIOS) {
      selectProdutoRef.current.focus();
    }
  }, [produto]);

  useEffect(() => {
    if (produtoCorTamanhoId && tabelaPrecoId && !operacaoItemEditando) {
      obterPrecoProdutoSelecionado();
    }
  }, [
    produtoCorTamanhoId,
    tabelaPrecoId,
    obterPrecoProdutoSelecionado,
    operacaoItemEditando,
  ]);

  return (
    <>
      {viewIsLoading && <LoadingPadrao />}
      {operacaoItemEditando && isLargerThan900 && <Overlay />}
      <VStack
        bg="gray.50"
        spacing={6}
        pl={{ base: 6, md: 12 }}
        pr={6}
        py={6}
        w="full"
        sx={{ '& .chakra-fade': { w: 'full' } }}
        position="relative"
        zIndex={operacaoItemEditando && isLargerThan900 ? 'modal' : 'unset'}
      >
        <SelectProdutoProvider
          handleCreateOperacaoItem={lancarProdutoNaOperacao}
          onChange={(novoProdutoSendoAdicionado: {
            value: string;
            label: string;
            codigoBarrasInterno?: string;
          }) => {
            const produtoSendoAdicionadoPelaConsultaProdutos =
              !!novoProdutoSendoAdicionado?.codigoBarrasInterno;
            buscarInformacoesProduto(novoProdutoSendoAdicionado);
            trocarFocoAoLancarProduto(
              produtoSendoAdicionadoPelaConsultaProdutos
            );
            onModalSelectProdutoClose();
          }}
        >
          <SelectProdutoContext.Consumer>
            {({
              onScannedCodeBar,
              setInputValue,
              getPaginatedProductsOptions,
              onChange: handleSelectProdutoChange,
            }) => {
              return (
                <>
                  {asMobileView && (
                    <Flex
                      w="full"
                      alignItems="center"
                      justifyContent="space-between"
                    >
                      <Button
                        variant="link"
                        colorScheme="purple"
                        p="1"
                        py="0"
                        fontWeight="normal"
                        leftIcon={
                          <Icon as={SalvarInserirNovoIcon} boxSize="5" />
                        }
                        onClick={onModalSelectProdutoOpen}
                        isDisabled={possuiMovimentacaoFinanceira}
                      >
                        Adicionar produto
                      </Button>

                      <IconButton
                        aria-label="Leitor de códigos de barras"
                        icon={<Icon as={LeitorCodigoBarrasIcon} boxSize="6" />}
                        variant="link"
                        minW="0"
                        p="0"
                        borderRadius="sm"
                        onClick={onModalCodigoBarrasOpen}
                        isDisabled={possuiMovimentacaoFinanceira}
                      />

                      <ModalCodigoBarras
                        isOpen={isModalCodigoBarrasOpen}
                        onClose={onModalCodigoBarrasClose}
                        onCodigoBarrasScanned={async (codigoBarras) => {
                          const { hasOptions } = await onScannedCodeBar(
                            codigoBarras
                          );

                          if (hasOptions) {
                            setInputValue(codigoBarras);

                            onModalSelectProdutoOpen();
                            selectProdutoRef.current?.openMenu();
                          }

                          onModalCodigoBarrasClose();
                        }}
                      />
                    </Flex>
                  )}

                  <MobileSelectWrapperModal
                    asMobileView={asMobileView}
                    isOpen={isModalSelectProdutoOpen}
                    onClose={onModalSelectProdutoClose}
                    initialFocusRef={selectProdutoRef}
                    isLoading={viewIsLoading}
                    footerComponent={
                      <Button
                        borderRadius="full"
                        colorScheme="secondary"
                        variant="solid"
                        fontSize={['16px', '16px', '14px']}
                        height={['40px', '40px', '32px']}
                        ref={refButtonConsultaProdutos}
                        margin="0 0 0 auto"
                        isDisabled={isLoading}
                        width={['full', 'full', '200px']}
                        onClick={() => {
                          handleAbrirConsultaProdutos({
                            getPaginatedProductsOptions,
                            handleSelectProdutoChange,
                            setInputValue,
                          });
                        }}
                      >
                        Consultar produtos
                      </Button>
                    }
                  >
                    {() =>
                      operacaoItemEditando ? (
                        <CampoContainer
                          name="produtoDescricao"
                          id="produtoDescricao"
                        >
                          <Input
                            isDisabled
                            size={isLargerThan900 ? 'lg' : 'md'}
                            value={`${operacaoItemEditando.produto}${
                              operacaoItemEditando.cor
                                ? ` | ${operacaoItemEditando.cor}`
                                : ''
                            }`}
                            _disabled={{
                              background: 'gray.50',
                              boxShadow: '0px 0px 4px #00000029',
                            }}
                          />
                        </CampoContainer>
                      ) : (
                        <Flex
                          width="full"
                          flexDir={isLargerThan900 ? 'column' : 'row'}
                          gap={isLargerThan900 ? '8px' : '0px'}
                        >
                          <Box
                            w={isLargerThan900 ? '100%' : 'calc(100% - 64px)'}
                          >
                            <SelectProduto
                              ref={selectProdutoRef}
                              value={produto}
                              autoFocus={!asMobileView}
                              mobileViewAlreadyWrapped={asMobileView}
                            />
                          </Box>
                          {!isLargerThan900 && (
                            <IconButton
                              aria-label="Leitor de códigos de barras"
                              icon={
                                <Icon as={LeitorCodigoBarrasIcon} boxSize="6" />
                              }
                              variant="link"
                              borderRadius="sm"
                              onClick={onModalCodigoBarrasOpen}
                              isDisabled={possuiMovimentacaoFinanceira}
                            />
                          )}
                          <Button
                            borderRadius="full"
                            colorScheme="secondary"
                            variant="solid"
                            display="none"
                            fontSize={['16px', '16px', '14px']}
                            height={['40px', '40px', '32px']}
                            ref={refButtonConsultaProdutos}
                            margin="0 0 0 auto"
                            isDisabled={isLoading}
                            width={['full', 'full', '200px']}
                            onClick={() => {
                              handleAbrirConsultaProdutos({
                                getPaginatedProductsOptions,
                                handleSelectProdutoChange,
                                setInputValue,
                              });
                            }}
                          >
                            Consultar produtos
                          </Button>
                        </Flex>
                      )
                    }
                  </MobileSelectWrapperModal>
                </>
              );
            }}
          </SelectProdutoContext.Consumer>
        </SelectProdutoProvider>

        <FormProvider {...formMethods}>
          <Fade in={!!produto || !!operacaoItemEditando} unmountOnExit>
            <ModalAdicionarEditarItem
              asMobileView={asMobileView}
              title={
                produto
                  ? `${nomeProduto}${produto?.cor ? ` - ${produto.cor}` : ''}`
                  : operacaoItemEditando
                  ? `${operacaoItemEditando.produto}${
                      operacaoItemEditando.cor
                        ? ` - ${operacaoItemEditando.cor}`
                        : ''
                    }`
                  : ''
              }
              isOpen={!!produto || !!operacaoItemEditando}
              onClose={() => {
                setOperacaoItemEditando(undefined);
                limparProduto();
              }}
            >
              <Box
                bg="white"
                h="100%"
                w="100%"
                p="20px"
                borderRadius="md"
                boxShadow="0px 0px 4px 0px #00000029"
                position="relative"
              >
                {possuiPromocao && isLargerThan900 && (
                  <Box
                    position="absolute"
                    top="0px"
                    left="0px"
                    w="fit-content"
                    height="fit-content"
                    zIndex="1"
                  >
                    <TagPromocao />
                  </Box>
                )}
                <form onSubmit={handleSubmit}>
                  <ManterFoco
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <VStack spacing={6} w="full">
                      <Box h="100%" w="full">
                        <Grid
                          templateColumns={{
                            base: '1fr auto',
                            sm: '1fr 1fr auto',
                            md: 'auto 1fr auto',
                          }}
                          w="full"
                          h="fit-content"
                          gap={{ base: 4, lg: 3, xl: 6 }}
                          justifyItems="end"
                        >
                          <GridItem
                            colStart={asMobileView ? { base: 2, sm: 3 } : 1}
                            rowSpan={{
                              base: 2,
                              md: 5,
                            }}
                            position="relative"
                          >
                            {!isLargerThan900 && possuiPromocao && (
                              <Box
                                position="absolute"
                                bottom="0px"
                                zIndex="1"
                                height="24px"
                                w="full"
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                borderBottomRadius="md"
                                bg="radial-gradient(closest-side at 50% 50%, #FFDA41 10%, #FF9E41 135%) 0% 0%"
                              >
                                <TagPromocaoMobile fontSize="60px" />
                              </Box>
                            )}
                            <AspectRatio
                              w={{
                                base: possuiPromocao ? '124px' : '118px',
                                md: '266px',
                                lg: '250px',
                                xl: '315px',
                              }}
                              ratio={1}
                              mt={{ base: '18px', md: '0' }}
                            >
                              <Flex
                                justifyContent="center"
                                alignItems="center"
                                minH="100%"
                                w="100%"
                                minW="106px"
                                maxW={{
                                  base: possuiPromocao ? '124px' : '118px',
                                  md: '266px',
                                  lg: '250px',
                                  xl: '315px',
                                }}
                                borderRadius="md"
                                overflow="hidden"
                                boxShadow="0px 0px 4px 0px #00000029"
                              >
                                <AspectRatio w="100%" ratio={1}>
                                  {produto && fotoProduto ? (
                                    <Image
                                      boxSize="500px"
                                      objectFit="cover"
                                      src={fotoProduto}
                                      alt={produto?.nome || ''}
                                    />
                                  ) : (
                                    <Flex
                                      h="full"
                                      alignItems="center"
                                      justifyContent="center"
                                      p={6}
                                    >
                                      <Icon
                                        as={ProdutoSemImagemIcon}
                                        color="gray.700"
                                        h="full"
                                        w="full"
                                        maxW={20}
                                      />
                                    </Flex>
                                  )}
                                </AspectRatio>
                              </Flex>
                            </AspectRatio>
                          </GridItem>

                          {produtoTemTamanhos && (
                            <>
                              {!asMobileView && (
                                <Flex
                                  justifyContent="flex-end"
                                  alignItems="center"
                                >
                                  <Text
                                    as="label"
                                    fontSize={{
                                      base: 'xs',
                                      xl: 'md',
                                    }}
                                    mb={0}
                                    htmlFor="produtoCorTamanhoId"
                                  >
                                    Tamanho
                                  </Text>
                                </Flex>
                              )}

                              <GridItem
                                colStart={asMobileView ? 1 : 3}
                                rowStart={1}
                                w="full"
                              >
                                <Flex
                                  alignItems="center"
                                  w="full"
                                  minW={{
                                    base: '105px',
                                    md: '128px',
                                  }}
                                  maxW={
                                    asMobileView
                                      ? 'full'
                                      : { base: 'full', md: '180px' }
                                  }
                                >
                                  <SelectPadrao
                                    id="produtoCorTamanhoId"
                                    name="produtoCorTamanhoId"
                                    label={asMobileView ? 'Tamanho' : ''}
                                    onSelect={(option: { value: string }) => {
                                      if (!option) return;
                                    }}
                                    options={
                                      tamanhosProduto
                                        ? tamanhosProduto
                                            ?.filter(
                                              (item) => !item.padraoSistema
                                            )
                                            .map((tamanho) => ({
                                              label: tamanho.nome,
                                              value: tamanho.id,
                                            }))
                                        : []
                                    }
                                    placeholder="Selecionar"
                                    size={
                                      asMobileView
                                        ? 'lg'
                                        : !isLargerThan1800
                                        ? 'md'
                                        : 'lg'
                                    }
                                    isSearchable={false}
                                    styles={{
                                      valueContainer: (provided) => ({
                                        ...provided,
                                        fontSize: isLargerThan900
                                          ? fontSizes.md
                                          : fontSizes.sm,
                                      }),
                                    }}
                                    autoFocus
                                  />
                                </Flex>
                              </GridItem>
                            </>
                          )}
                          {!asMobileView && (
                            <Flex justifyContent="flex-end" alignItems="center">
                              <Text
                                as="label"
                                fontSize={{ base: 'xs', xl: 'md' }}
                                mb={0}
                                htmlFor="quantidade"
                                fontWeight="semibold"
                                color="gray.700"
                                lineHeight="none"
                              >
                                Quantidade
                              </Text>
                            </Flex>
                          )}
                          <GridItem
                            colStart={
                              asMobileView
                                ? produtoTemTamanhos
                                  ? { base: 1, sm: 2 }
                                  : { base: 1, sm: 1 }
                                : 3
                            }
                            rowStart={
                              produtoTemTamanhos ? { base: 2, sm: 1, md: 2 } : 1
                            }
                            w="full"
                          >
                            {asMobileView && (
                              <Text
                                as="label"
                                fontSize="sm"
                                mb={0}
                                htmlFor="quantidade"
                                fontWeight="semibold"
                                color="gray.700"
                                lineHeight="1"
                              >
                                Quantidade
                              </Text>
                            )}
                            <HStack
                              spacing="3px"
                              alignItems="flex-end"
                              w="full"
                              minW={{ base: '105px', md: '128px' }}
                              maxW={{ base: 'full', md: '180px' }}
                              mb={asMobileView ? '2' : '0'}
                            >
                              {isLargerThan360 && (
                                <IconButton
                                  aria-label="Decrementar"
                                  icon={<Icon as={FiMinus} color="black" />}
                                  variant="outline"
                                  borderColor="gray.200"
                                  _hover={{
                                    bg: 'transparent',
                                    borderColor: 'gray.300',
                                    _disabled: {
                                      borderColor: 'gray.200',
                                      bg: 'gray.50',
                                    },
                                  }}
                                  _disabled={{
                                    background: 'gray.50',
                                  }}
                                  _active={{ bg: 'gray.50' }}
                                  borderRadius="md"
                                  size={
                                    asMobileView
                                      ? 'lg'
                                      : !isLargerThan1800
                                      ? 'md'
                                      : 'lg'
                                  }
                                  w="full"
                                  minW={{ base: '1px', sm: 9 }}
                                  maxW={
                                    asMobileView
                                      ? '10'
                                      : !isLargerThan1800
                                      ? '9'
                                      : '10'
                                  }
                                  onClick={handleDecrementarQuantidade}
                                  isDisabled={
                                    quantidade === 1 || produtoConsignado
                                  }
                                />
                              )}

                              <NumberInput
                                id="quantidade"
                                isDisabled={produtoConsignado}
                                name="quantidade"
                                errorMessageStyleProps={{
                                  whiteSpace: 'nowrap',
                                  transform: 'translateX(-42px)',
                                }}
                                pattern="^[0-9.,]+$"
                                scale={casasDecimaisDoCampoQuantidade}
                                size={
                                  asMobileView
                                    ? 'lg'
                                    : !isLargerThan1800
                                    ? 'md'
                                    : 'lg'
                                }
                                fontSize={{ base: 'sm', md: 'md' }}
                                autoFocus={!produtoTemTamanhos && !isMobile}
                                minW="80px"
                                pr="0px"
                                textAlign="center"
                                max={valorMaximoQuantidade}
                              />

                              {isLargerThan360 && (
                                <IconButton
                                  aria-label="Incrementar"
                                  icon={<Icon as={FiPlus} color="black" />}
                                  variant="outline"
                                  borderColor="gray.200"
                                  isDisabled={
                                    produtoConsignado ||
                                    quantidade >= valorMaximoQuantidade
                                  }
                                  _hover={{
                                    bg: 'transparent',
                                    borderColor: 'gray.300',
                                    _disabled: { borderColor: 'gray.200' },
                                  }}
                                  _active={{ bg: 'gray.100' }}
                                  borderRadius="md"
                                  size={
                                    asMobileView
                                      ? 'lg'
                                      : !isLargerThan1800
                                      ? 'md'
                                      : 'lg'
                                  }
                                  w="full"
                                  minW={{ base: '1px', sm: 9 }}
                                  maxW={
                                    asMobileView
                                      ? '10'
                                      : !isLargerThan1800
                                      ? '9'
                                      : '10'
                                  }
                                  onClick={handleIncrementarQuantidade}
                                />
                              )}
                            </HStack>
                          </GridItem>
                          {!asMobileView && (
                            <Flex
                              mt={
                                formMethods.formState.errors.quantidade ===
                                undefined
                                  ? ''
                                  : '10px'
                              }
                              justifyContent="flex-end"
                              alignItems="center"
                              minW="80px"
                            >
                              <Text
                                as="label"
                                fontSize={{ base: 'xs', xl: 'md' }}
                                mb={0}
                                htmlFor="valorUnitario"
                                whiteSpace="nowrap"
                                fontWeight="semibold"
                                color="gray.700"
                              >
                                Valor unitário
                              </Text>
                            </Flex>
                          )}
                          <GridItem
                            colSpan={{
                              base: produtoTemTamanhos ? 2 : 1,
                              sm: 1,
                            }}
                            rowStart={
                              asMobileView
                                ? {
                                    base: produtoTemTamanhos ? 3 : 2,
                                    sm: produtoTemTamanhos ? 2 : 1,
                                  }
                                : undefined
                            }
                            w="full"
                          >
                            <Flex
                              mt={
                                formMethods.formState.errors.quantidade ===
                                undefined
                                  ? ''
                                  : '10px'
                              }
                              alignItems="center"
                              w="full"
                              minW={{ base: '105px', md: '128px' }}
                              maxW={{ base: 'full', md: '180px' }}
                              position="relative"
                            >
                              {naoPermitirAlterarValorNaVenda &&
                              possuiPromocao ? (
                                <VStack
                                  w="full"
                                  justify="flex-start"
                                  align="flex-start"
                                  gap="0px"
                                  spacing="4px"
                                >
                                  {asMobileView && (
                                    <FormLabel mb="0px" lineHeight="1">
                                      Valor unitário
                                    </FormLabel>
                                  )}
                                  <Flex
                                    color="black"
                                    borderRadius="5px"
                                    w="full"
                                    height="48px"
                                    bg="radial-gradient(closest-side at 50% 50%, #FFDA41 10%, #FF9E41 135%) 0% 0%"
                                    justifyContent="center"
                                    pr="12px"
                                    flexDir="column"
                                    align="flex-end"
                                    py="6px"
                                    gap="4px"
                                  >
                                    <TextValor
                                      valor={valorUnitario}
                                      casasDecimais={
                                        casasDecimais?.casasDecimaisValor
                                      }
                                      fontSize="16px"
                                      color="black"
                                      fontWeight="black"
                                      symbolFontWeight="black"
                                      symbolFontSize="12px"
                                    />
                                    <TextValor
                                      valor={valorUnitarioOriginal}
                                      casasDecimais={
                                        casasDecimais?.casasDecimaisValor
                                      }
                                      fontSize="12px"
                                      fontWeight="400"
                                      textDecor="line-through"
                                      color="gray.700"
                                      symbolFontWeight="gray.700"
                                      symbolFontSize="10px"
                                    />
                                  </Flex>
                                </VStack>
                              ) : (
                                <InputNumber
                                  id="valorUnitario"
                                  name="valorUnitario"
                                  _disabled={{
                                    borderColor: 'gray.200',
                                    borderWidth: '1px',
                                    borderStyle: 'solid',
                                  }}
                                  label={asMobileView ? 'Valor unitário' : ''}
                                  prefix="R$"
                                  pattern="(^[\d-\)\(]+$)"
                                  borderRadius="5px"
                                  scale={casasDecimais?.casasDecimaisValor}
                                  variant={
                                    naoPermitirAlterarValorNaVenda
                                      ? 'none'
                                      : 'outline'
                                  }
                                  background={
                                    naoPermitirAlterarValorNaVenda
                                      ? 'gray.50'
                                      : 'white'
                                  }
                                  color={possuiPromocao ? 'black' : 'gray.700'}
                                  disabled={naoPermitirAlterarValorNaVenda}
                                  size={
                                    asMobileView
                                      ? 'lg'
                                      : !isLargerThan1800
                                      ? 'md'
                                      : 'lg'
                                  }
                                  fontSize={{ base: 'sm', md: 'md' }}
                                  fontWeight={
                                    possuiPromocao &&
                                    naoPermitirAlterarValorNaVenda
                                      ? '700'
                                      : '500'
                                  }
                                  max={valorMaximoValorUnitario}
                                />
                              )}
                            </Flex>
                          </GridItem>
                          {!asMobileView && (
                            <Flex justifyContent="flex-end" alignItems="center">
                              <Text
                                as="label"
                                fontSize={{ base: 'xs', xl: 'md' }}
                                mb={0}
                                htmlFor="descontoItem"
                                fontWeight="semibold"
                                color="gray.700"
                              >
                                Desconto
                              </Text>
                            </Flex>
                          )}
                          <GridItem
                            position="relative"
                            colSpan={{ base: 2, sm: 1 }}
                            w="full"
                            mb={
                              valorDesconto === 0 && asMobileView ? '' : '10px'
                            }
                          >
                            <Flex
                              alignItems="center"
                              direction="column"
                              w="full"
                              minW={{ base: '105px', md: '128px' }}
                              maxW={{ base: 'full', md: '180px' }}
                            >
                              {asMobileView && (
                                <Flex
                                  justifyContent="flex-start"
                                  alignItems="center"
                                  w="full"
                                >
                                  <Text
                                    as="label"
                                    mb={0}
                                    fontSize="sm"
                                    htmlFor="descontoItem"
                                    fontWeight="semibold"
                                    color="gray.700"
                                  >
                                    Desconto
                                  </Text>
                                </Flex>
                              )}

                              <HStack spacing={2} w="full">
                                <Box
                                  sx={{
                                    '& div.react-select__control div.react-select__value-container div.react-select__single-value':
                                      {
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        h: 'full',
                                        ml: 0,
                                        mr: 0,
                                        maxW: 'none',
                                        position: 'sticky',
                                        top: 0,
                                        transform: 'none',
                                        fontSize: 'xs',
                                      },
                                  }}
                                >
                                  <ButtonItemDesconto
                                    id="tipoDescontoItem"
                                    name="tipoDescontoItem"
                                    size={
                                      asMobileView
                                        ? 'lg'
                                        : !isLargerThan1800
                                        ? 'md'
                                        : 'lg'
                                    }
                                    fontSize={{
                                      base: 'xs',
                                      xl: 'md',
                                    }}
                                    bg="white"
                                    _hover={{
                                      background: 'gray.50',
                                    }}
                                    _active={{
                                      background: 'white',
                                    }}
                                    variant="outline"
                                    borderColor="gray.200"
                                    rightIcon={<FiChevronDown />}
                                  />
                                </Box>
                                <InputNumber
                                  id="descontoItem"
                                  name="descontoItem"
                                  max={
                                    tipoDescontoItem ===
                                    TipoValorEnum.PORCENTAGEM
                                      ? 100
                                      : quantidade * (valorUnitario as number)
                                  }
                                  size={
                                    asMobileView
                                      ? 'lg'
                                      : !isLargerThan1800
                                      ? 'md'
                                      : 'lg'
                                  }
                                  pattern="(^[\d-\)\(]+$)"
                                  fontSize={{ base: 'sm', md: 'md' }}
                                />
                              </HStack>
                            </Flex>
                            {valorDesconto !== 0 && (
                              <Text
                                position="absolute"
                                textAlign="right"
                                fontSize="10px"
                                mt="5px"
                              >{`Valor do desconto: ${moneyMask(
                                valorDesconto,
                                true
                              )}`}</Text>
                            )}
                          </GridItem>

                          {!asMobileView && (
                            <Flex justifyContent="flex-end" alignItems="center">
                              <Text
                                as="label"
                                fontSize={{ base: 'xs', xl: 'md' }}
                                mb={0}
                                htmlFor="valorTotal"
                                fontWeight="semibold"
                                color="gray.700"
                              >
                                Valor total
                              </Text>
                            </Flex>
                          )}
                          <GridItem colSpan={{ base: 2, sm: 1 }} w="full">
                            <Flex
                              alignItems="center"
                              w="full"
                              mt="3px"
                              minW={{ base: '105px', md: '128px' }}
                              maxW={{ base: 'full', md: '180px' }}
                            >
                              <InputNumber
                                id="valorTotal"
                                name="valorTotal"
                                label={asMobileView ? 'Valor total' : ''}
                                prefix="R$"
                                onChange={() => {}}
                                max={valorMaximoValorUnitario}
                                isDisabled
                                _disabled={{
                                  background: possuiPromocao
                                    ? 'gray.50'
                                    : 'secondary.300',
                                  fontWeight: possuiPromocao ? '700' : '500',
                                }}
                                scale={2}
                                size={
                                  asMobileView
                                    ? 'lg'
                                    : !isLargerThan1800
                                    ? 'md'
                                    : 'lg'
                                }
                                fontSize={{ base: 'sm', md: 'md' }}
                              />
                            </Flex>
                          </GridItem>
                          {asMobileView && !operacaoItemEditando && (
                            <GridItem
                              colStart={{
                                base: 1,
                                sm: produtoTemTamanhos ? 2 : 1,
                              }}
                              colSpan={produtoTemTamanhos ? 2 : 3}
                              w="full"
                            >
                              <Flex
                                alignItems="flex-end"
                                w="full"
                                h="full"
                                pt={{ base: 4, sm: 0 }}
                              >
                                <ButtonDefault
                                  colorScheme="purple"
                                  height="40px"
                                  fontWeight="300"
                                  width="full"
                                  isDisabled={viewIsLoading}
                                  isLoading={viewIsLoading}
                                  color="white"
                                  _focus={{
                                    border: '2px solid rgba(85, 2, 178, 0.5)',
                                    bg: 'secondary.400',
                                    color: 'black',
                                    '&:hover': {
                                      bg: 'secondary.400',
                                    },
                                    '&:active': {
                                      bg: 'secondary.500',
                                      color: 'black',
                                    },
                                  }}
                                  _active={{
                                    bg: 'purple.500',
                                    color: 'white',
                                    '&:hover': {
                                      bg: 'purple.500',
                                      color: 'white',
                                    },
                                  }}
                                  type="submit"
                                  onClick={() => {
                                    if (vendedor.nome === undefined) {
                                      toast.warning(
                                        'Nenhum vendedor foi selecionado'
                                      );
                                    }
                                  }}
                                  size={asMobileView ? 'lg' : 'md'}
                                >
                                  Confirmar item
                                </ButtonDefault>
                              </Flex>
                            </GridItem>
                          )}
                        </Grid>
                      </Box>

                      {!operacaoItemEditando && !asMobileView && (
                        <ButtonDefault
                          colorScheme="purple"
                          height="40px"
                          fontWeight="300"
                          width="full"
                          type="submit"
                          isLoading={viewIsLoading}
                          isDisabled={viewIsLoading}
                          color="white"
                          _focus={{
                            border: '2px solid rgba(85, 2, 178, 0.5)',
                            bg: 'secondary.400',
                            color: 'black',
                            '&:hover': {
                              bg: 'secondary.400',
                            },
                            '&:active': {
                              bg: 'secondary.500',
                              color: 'black',
                            },
                          }}
                          _active={{
                            bg: 'purple.500',
                            color: 'white',
                            '&:hover': {
                              bg: 'purple.500',
                              color: 'white',
                            },
                          }}
                          onClick={() => {
                            if (viewIsLoading) return;
                            if (vendedor.nome === undefined) {
                              toast.warning('Nenhum vendedor foi selecionado');
                            }
                          }}
                          size={asMobileView ? 'lg' : 'md'}
                        >
                          Confirmar item
                        </ButtonDefault>
                      )}
                      {operacaoItemEditando && asMobileView && (
                        <Stack
                          direction={{ base: 'column', sm: 'row' }}
                          spacing="4"
                          w="full"
                        >
                          <Button
                            variant="outlineDefault"
                            onClick={() => {
                              setOperacaoItemEditando(undefined);
                              limparProduto();
                            }}
                            size="lg"
                            borderRadius="full"
                            w="full"
                          >
                            Cancelar
                          </Button>
                          <Button
                            colorScheme="purple"
                            onClick={handleSubmit}
                            size="lg"
                            isLoading={viewIsLoading}
                            w="full"
                            type="submit"
                            fontWeight="300"
                            color="white"
                            _focus={{
                              border: '2px solid rgba(85, 2, 178, 0.5)',
                              bg: 'secondary.400',
                              color: 'black',
                              '&:hover': {
                                bg: 'secondary.400',
                              },
                              '&:active': {
                                bg: 'secondary.500',
                                color: 'black',
                              },
                            }}
                            _active={{
                              bg: 'purple.500',
                              color: 'white',
                              '&:hover': {
                                bg: 'purple.500',
                                color: 'white',
                              },
                            }}
                          >
                            Confirmar alteração
                          </Button>
                        </Stack>
                      )}
                    </VStack>
                  </ManterFoco>
                </form>
              </Box>
            </ModalAdicionarEditarItem>
          </Fade>
          {operacaoItemEditando && (
            <HStack spacing={3}>
              <Button
                variant="outlineDefault"
                w="96px"
                onClick={() => {
                  setOperacaoItemEditando(undefined);
                  limparProduto();
                }}
                size={asMobileView ? 'lg' : 'md'}
              >
                Cancelar
              </Button>
              <Button
                colorScheme="purple"
                isLoading={viewIsLoading}
                onClick={handleSubmit}
                size={asMobileView ? 'lg' : 'md'}
                fontWeight="300"
                type="submit"
                color="white"
                _focus={{
                  border: '2px solid rgba(85, 2, 178, 0.5)',
                  bg: 'secondary.400',
                  color: 'black',
                  '&:hover': {
                    bg: 'secondary.400',
                  },
                  '&:active': {
                    bg: 'secondary.500',
                    color: 'black',
                  },
                }}
                _active={{
                  bg: 'purple.500',
                  color: 'white',
                  '&:hover': {
                    bg: 'purple.500',
                    color: 'white',
                  },
                }}
              >
                Confirmar alteração
              </Button>
            </HStack>
          )}
        </FormProvider>
      </VStack>
      <GlobalHotKeys handlers={handlersHotKeys} keyMap={keyMap} />
    </>
  );
};

export default AdicionarEditarItem;
