import React, { useState, useEffect } from 'react';
import { RouteComponentProps, useHistory } from 'react-router-dom';
import { FormProvider } from 'react-hook-form';
import { Box } from '@chakra-ui/react';
import { toast } from 'react-toastify';

import ProdutosFormularioProvider, {
  ProdutoRefProps,
  ProdutosFormularioContext,
} from 'store/Produtos/ProdutosFormulario';
import { ProdutoPrecoLoja, ProdutoProps } from 'types/produto';
import ConstanteRotas from 'constants/rotas';
import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import TipoProdutoEnum from 'constants/enum/tipoProduto';

import { ContainerListagem } from 'components/Layout/Listagem/containerListagem';
import { ModalDesistencia } from 'components/Modal/ModalDesistencia';

import { useForm, yupResolver } from '../validationForm';
import { InformacoesProdutos } from '../Types';
import { CadastroItem } from './CadastroItem';

type TParams = { id?: string };

export type ProdutosProps = {
  referencia: string;
  tipoProduto: number;
  foto: string;
};

interface CadastrarProps extends RouteComponentProps<TParams> {
  cadastroExterno?: boolean;
  nomeProduto?: string;
  cadastroSucessoCallback?: (produto: ProdutoProps) => void;
  cadastroCancelar?: () => void;
  listProducts?: ProdutosProps;
  produtos?: InformacoesProdutos;
}

const Cadastrar = ({
  match,
  produtos,
  cadastroExterno = false,
  nomeProduto,
  listProducts,
  cadastroCancelar,
  cadastroSucessoCallback,
}: CadastrarProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isDuplicado, setIsDuplicado] = useState(false);

  const history = useHistory();

  const formMethods = useForm({
    resolver: yupResolver,
    defaultValues: {
      ...(nomeProduto ? { nome: nomeProduto } : {}),
      ...produtos,
      ...listProducts,
      valorUnitario: produtos?.valorUnitario ?? 0,
      cores: [],
      tamanhos: [],
    },
    shouldUnregister: true,
  });

  const { setValue } = formMethods;

  const processRemovedProductRoute = () => {
    if (cadastroCancelar) {
      cadastroCancelar();
    } else {
      history.push(ConstanteRotas.PRODUTO);
    }
  };

  const deleteProduct = async (produtoId: string) => {
    setIsLoading(true);
    const response = await api.delete<void, ResponseApi>(
      ConstanteEnderecoWebservice.PRODUTO_EXCLUIR,
      {
        params: { id: produtoId },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item) => toast.success(item));
      }
      if (response.sucesso) {
        toast.success('O cadastro foi removido com sucesso.');
        processRemovedProductRoute();
        setIsLoading(false);
      }
    }
    setIsLoading(false);
  };

  const desistProduct = async (produtoId: string) => {
    await ModalDesistencia({
      title: 'Desistir do cadastro',
      labelButtonCancelar: 'Cancelar',
      labelButtonConfirmar: 'Excluir produto',
      textoMensagem:
        'Todos os dados informados serão removidos e o cadastro cancelado. \nDeseja excluir o produto?',
      callback: async () => {
        await deleteProduct(produtoId);
      },
    });
  };

  const handleCancelSubmit = async (idProduto: string) => {
    if (!idProduto) {
      processRemovedProductRoute();
    } else {
      desistProduct(idProduto);
    }
  };

  const desistirCadastro = async (id: string) => {
    const title = `Informe uma variação!`;

    const message = `É necessário cadastrar ao menos uma variação. Se o produto não  possui variações, retorne para o card principal e altere o tipo do produto para “Simples”. Se preferir, você pode desistir do cadastro e todas as informações serão removidas.`;

    await ModalDesistencia({
      title,
      labelButtonCancelar: 'Desistir do cadastro',
      labelButtonConfirmar: 'Continuar editando',
      textoMensagem: message,
      callback: () => {
        setIsLoading(false);
      },
      callbackCancel: () => {
        desistProduct(id);
      },
    });
  };

  const handleSubmit = async (
    ref: React.RefObject<ProdutoRefProps>,
    nome: string,
    idProduto: string,
    precoPorLoja: ProdutoPrecoLoja[],
    temVariacoes: (
      id: string,
      produtoSendoCadastrado: boolean
    ) => Promise<boolean>
  ) => {
    if (!ref.current?.submitFormularioProduto) {
      if (cadastroSucessoCallback) {
        cadastroSucessoCallback({
          nome,
          id: idProduto,
          produtoPrecoLojas: precoPorLoja,
        });

        return;
      }

      history.push(ConstanteRotas.PRODUTO);
      return;
    }

    const dadosCadastro = await ref.current.submitFormularioProduto();

    if (!dadosCadastro?.sucesso) {
      return;
    }

    if (
      dadosCadastro?.produto?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO
    ) {
      const temVariacao = await temVariacoes(dadosCadastro?.produto?.id, true);

      if (!temVariacao) {
        desistirCadastro(dadosCadastro?.produto?.id || '');
        return;
      }
    }

    if (cadastroSucessoCallback) {
      cadastroSucessoCallback({
        nome: dadosCadastro.produto?.nome || '',
        id: dadosCadastro.produto?.id || idProduto,
        produtoPrecoLojas: precoPorLoja,
      });

      return;
    }

    history.push(ConstanteRotas.PRODUTO);
  };

  useEffect(() => {
    if (match.params.id) {
      setIsDuplicado(true);
    }
  }, [match]);

  useEffect(() => {
    setIsLoading(true);
    const getNcmImportacaoXml = async () => {
      if (produtos && produtos?.ncm) {
        let ncm = {
          codigo: '',
          descricao: '',
        };

        const response = await api.get<
          void,
          ResponseApi<
            {
              codigo: string;
              descricao: string;
            }[]
          >
        >(ConstanteEnderecoWebservice.NCM_LISTAR_SELECT, {
          params: { descricaoCodigo: produtos?.ncm },
        });

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
          }

          if (response.sucesso) {
            ncm = {
              codigo: response.dados[0]?.codigo,
              descricao: response.dados[0]?.descricao,
            };
          }

          if (response.dados && response.sucesso) {
            setValue('codigoNcm', {
              label: `${ncm.codigo} - ${ncm.descricao}`,
              value: ncm.codigo,
            });
            setIsLoading(false);
          }
          setIsLoading(false);
        }
      }
      setIsLoading(false);
    };

    getNcmImportacaoXml();
  }, [produtos, setValue]);

  return (
    <Box h="100%" minH="99vh" bg="gray.100">
      <FormProvider {...formMethods}>
        <ProdutosFormularioProvider
          action="cadastrar"
          cadastroExterno={cadastroExterno}
          isLoading={isLoading}
          valueProdutoXlm={produtos}
          setIsLoading={setIsLoading}
          isDuplicado={isDuplicado}
        >
          <ProdutosFormularioContext.Consumer>
            {({
              refSalvarProduto,
              nome,
              precoPorLoja,
              idProduto,
              temVariacoes,
            }) => {
              return (
                <ContainerListagem
                  formMethods={formMethods}
                  isForm={false}
                  isLoading={isLoading}
                  onSubmit={() => {
                    handleSubmit(
                      refSalvarProduto,
                      nome,
                      idProduto,
                      precoPorLoja,
                      temVariacoes
                    );
                  }}
                  textSubmit="Finalizar"
                  iconSubmit={false}
                  maxWidth="full"
                  containerPossuiFundo={false}
                  onSubmitReset={undefined}
                  onCancel={() => handleCancelSubmit(idProduto)}
                >
                  <CadastroItem deleteProduct={deleteProduct} />
                </ContainerListagem>
              );
            }}
          </ProdutosFormularioContext.Consumer>
        </ProdutosFormularioProvider>
      </FormProvider>
    </Box>
  );
};

export default Cadastrar;
