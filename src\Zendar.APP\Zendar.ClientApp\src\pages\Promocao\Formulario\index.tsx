import { Box, Flex } from '@chakra-ui/react';

import { Container } from './Components/Container';
import { DescricaoCampanha } from './Components/DescricaoCampanha';
import { LojasCampanha } from './Components/LojasCampanha';
import { PeriodoDuracaoCampanha } from './Components/PeriodoDuracaoCampanha';
import { Produtos } from './Components/Produtos';
import { useFormularioPromocao } from './hooks';

type FormularioProps = {
  isAlterar?: boolean;
  isReadOnly?: boolean;
};

export const Formulario = ({ isAlterar, isReadOnly }: FormularioProps) => {
  const { listaLojas } = useFormularioPromocao();

  return (
    <Box position="relative">
      <Flex
        gap={{ base: 7, md: 6 }}
        mb={14}
        flexDir={{ base: 'column', md: 'row' }}
      >
        <Box w={{ base: 'full', md: '50%' }}>
          <Container title="Descrição da campanha" h="full" overflowX="auto">
            <Flex minW="max" flexDir="column" gap="44px">
              <DescricaoCampanha isReadOnly={isReadOnly} />
            </Flex>
          </Container>
        </Box>
        <Box w={{ base: 'full', md: '50%' }}>
          <Container
            title="Período de duração da campanha"
            h="full"
            overflowX="auto"
          >
            <Flex flexDir="column" gap="44px">
              <PeriodoDuracaoCampanha isReadOnly={isReadOnly} />
            </Flex>
          </Container>
        </Box>
      </Flex>
      <Container
        title="Lista de produtos"
        px={{ base: 6 }}
        pt={{ base: 9 }}
        pb={{ base: 7 }}
        mb={7}
      >
        <Produtos isAlterar={isAlterar} isReadOnly={isReadOnly} />
      </Container>
      {listaLojas?.length > 1 && (
        <Container
          title="Selecione as lojas para aplicar esta campanha"
          overflowX="auto"
        >
          <LojasCampanha />
        </Container>
      )}
    </Box>
  );
};
