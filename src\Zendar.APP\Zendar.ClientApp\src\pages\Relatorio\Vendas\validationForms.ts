import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { setDateMinHours, setDateMaxHours } from 'helpers/data/setHoursDate';

import { IdentificacaoIntegracao } from 'constants/enum/IdentificacaoIntegracao';
import StatusOperacaoEnum, {
  statusOperacaoOptions,
} from 'constants/enum/statusOperacao';
import { TipoFiscal } from 'constants/enum/tipoFiscal';
import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';

export type SelectClienteProps = {
  id: string;
  nome: string;
  endereco: string;
  codigo: number;
  cpfCnpj: string;
};

export type OptionsProps = {
  label: string;
  value: string | number;
};

export type OptionResponseProps = {
  id: string;
  nome: string;
};

export type FormData = {
  dataEmissaoFim: Date;
  dataEmissaoInicio: Date;
  tipoRelatorio: number | null;
  clienteFornecedorId: OptionsProps | null;
  vendedorId: string | null;
  entregadorId: OptionsProps | null;
  statusConsulta?: null | {
    label: string;
    value: number;
  };
  origem?: number | null;
  tipoFiscal?: number[] | null;
  localEstoqueId?: string;
  inclusoHorasNoPeriodo?: boolean;
};

export const formDefaultValues: FormData = {
  dataEmissaoFim: setDateMaxHours(new Date()),
  dataEmissaoInicio: setDateMinHours(new Date()),
  clienteFornecedorId: null,
  vendedorId: null,
  entregadorId: null,
  tipoRelatorio: null,
  statusConsulta: statusOperacaoOptions.f,
  origem: IdentificacaoIntegracao.TODAS,
  tipoFiscal: [TipoFiscal.NFCE, TipoFiscal.NFE, TipoFiscal.SEM_FISCAL],
  localEstoqueId: undefined,
};

const schema = yup.object().shape({
  tipoRelatorio: yup
    .number()
    .nullable()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  dataEmissaoInicio: yup
    .date()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .test(
      'data-inicio-menor-final',
      'Data inicial maior que a final.',
      function (value) {
        const { dataEmissaoFim } = this.parent;
        return (
          !value ||
          !dataEmissaoFim ||
          new Date(value) <= new Date(dataEmissaoFim)
        );
      }
    ),
  dataEmissaoFim: yup
    .date()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .test(
      'data-final-maior-inicio',
      'Data final menor que a inicial.',
      function (value) {
        const { dataEmissaoInicio } = this.parent;
        return (
          !dataEmissaoInicio ||
          !value ||
          new Date(value) >= new Date(dataEmissaoInicio)
        );
      }
    ),
});

export const yupResolver = yupResolverInstance(schema);
